[package]
name = "bf-tree"
version = "0.3.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
rand = { version = "0.8.5", features = ["small_rng"] }
tracing = { version = "0.1", optional = true }
serde = { version = "1", features = ["derive"]}
auto_ops = { version = "0.3", optional = true }
hdrhistogram = { version = "7.5.4", optional = true }
chrono = { version = "0.4.38", optional = true }
variant_count = { version = "1.1.0", optional = true }
toml = "0.8.23"

[target.'cfg(target_os = "linux")'.dependencies]
io-uring = "0.6.4"
libc = "0.2.155"
spdk-rs = { git = "https://github.com/openebs/spdk-rs.git", branch = "release/2.5", optional = true }
crossbeam-queue = { version = "0.3.11", optional = true }

[target.'cfg(target_os = "windows")'.dependencies]
windows-sys = { version = "0.59.0", features = ["Win32_System_Threading", "Win32_System_WindowsProgramming"] }

[dev-dependencies]
proptest = "1.6.0"
proptest-derive = "0.5.1"
tempfile = "3.10.1"
shuttle = "0.7.1"
tracing-subscriber = "0.3"
rstest = "0.19.0"
bytemuck = "1.15.0"

[features]
shuttle = []
tracing = ["dep:tracing"]
spdk = ["dep:spdk-rs", "dep:crossbeam-queue"]

# Whether to compile metrics code
metrics = ["dep:auto_ops", "dep:hdrhistogram", "dep:chrono", "dep:variant_count"]
# Whether to enable metrics at runtime
metrics-rt = ["dep:auto_ops", "dep:hdrhistogram", "dep:chrono"]

[profile.release]
debug = true

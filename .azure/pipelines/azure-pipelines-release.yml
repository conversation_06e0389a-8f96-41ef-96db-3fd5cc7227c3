######################################
# Internal release for Bf-Tree
###################################### 
trigger:
  branches:
    include:
    - main
resources:
  repositories:
  - repository: self
    type: git
    ref: refs/heads/main

pool:
  vmImage: 'windows-latest'

jobs:
- job: Phase_1
  displayName: Assessment
  condition: succeeded()  # Ensures job execution stops if any step fails - need this step due to "condition" in GitHubRelease task
  cancelTimeoutInMinutes: 1
  pool:
    name: Azure Pipelines
    vmImage: 'windows-latest'
  steps:
  - checkout: self
    clean: False
    submodules: recursive
    persistCredentials: True
variables:
  buildPlatform: 'Any CPU'
  buildConfiguration: 'Release'
resources:
  repositories:
  - repository: self
    type: git
jobs: 
 - job: Phase_1 
   displayName: Assessment
   cancelTimeoutInMinutes: 1
   pool:
     name: Azure Pipelines
     vmImage: windows-latest
   steps:
   - checkout: self
     clean: False
     submodules: recursive
     persistCredentials: True
   - task: securedevelopmentteam.vss-secure-development-tools.build-task-credscan.CredScan@3
     name: CredScan6  
     displayName: Run CredScan
     continueOnError: True
     inputs:
       debugMode: false
       folderSuppression: false
       verboseOutput: true
   - task: securedevelopmentteam.vss-secure-development-tools.build-task-report.SdtReport@2
     name: SdtReport1
     displayName: 'Create Security Analysis Report'
     inputs:
       GdnExportTsvFile: true
   - task: securedevelopmentteam.vss-secure-development-tools.build-task-publishsecurityanalysislogs.PublishSecurityAnalysisLogs@3
     name: PublishSecurityAnalysisLogs12
     displayName: Publish Security Analysis Logs
     inputs:
       TargetPath: '\\my\share\$(Build.DefinitionName)\$(Build.BuildNumber)'
       AntiMalware: true
       APIScan: true
       CodesignValidation: true
       CredScan: true
       FortifySCA: true
       FxCop: true
       ModernCop: true
       MSRD: true
       SDLNativeRules: true
       Semmle: true
       TSLint: true
       WebScout: true
   - task: securedevelopmentteam.vss-secure-development-tools.build-task-postanalysis.PostAnalysis@2
     name: PostAnalysis13
     displayName: Post Analysis
     inputs:
       GdnBreakAllTools: false
       GdnBreakGdnToolCredScan: true
       GdnBreakGdnToolFxCop: true
       GdnBreakGdnToolFxCopSeverity: Error
       GdnBreakGdnToolSemmle: true

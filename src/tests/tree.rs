use std::hint::black_box;
use std::{collections::HashMap, ops::Range};

use crate::BfTree;
use proptest::{
    prelude::*,
    test_runner::{Config, TestRunner},
};
use proptest_derive::Arbitrary;
use std::path::Path;

#[derive(Clone, Arbitrary, Debug)]
enum TreeOp {
    Insert,
    Delete,
    Read,
}

fn tree_insert_read_delete_scan(
    input: Vec<(Vec<u8>, Vec<u8>, TreeOp)>,
    storage: &impl AsRef<Path>,
) {
    let mut model = HashMap::<Vec<u8>, Vec<u8>>::new();
    let tree = BfTree::new(storage, 1024 * 1024 * 32);

    let mut out_buffer = vec![0u8; 1024]; // Buffer for reading from tree

    let mut current_idx = 0;

    for (k, v, op) in input.iter() {
        match op {
            TreeOp::Insert => {
                tree.insert(k, v);
                model.insert(k.to_owned(), v.to_owned());
            }
            TreeOp::Delete => {
                let _ = tree.delete(k);
                model.remove(k);
            }
            TreeOp::Read => {
                let rt = tree.read(k, &mut out_buffer);
                match model.get(k) {
                    Some(model_v) => {
                        if model_v.len() != rt as usize {
                            let _stat = tree.get_stats();
                            let _rt = tree.read(k, &mut out_buffer);
                        }
                        assert_eq!(model_v.len(), rt as usize);
                        assert_eq!(&out_buffer[0..model_v.len()], model_v);
                    }
                    None => {
                        assert_eq!(rt, 0);
                    }
                }
            }
        }
        current_idx += 1;
    }
    black_box(current_idx);
}

fn leaf_insert_read_inner(len_range: Range<usize>, storage: &impl AsRef<Path>) {
    let config = Config {
        cases: 30,
        max_local_rejects: 1,
        max_global_rejects: 1,
        source_file: Some("src/tests/tree.rs"),
        ..Config::default()
    };

    let strategy = proptest::collection::vec(
        (
            proptest::collection::vec(any::<u8>(), 1..30), // Key
            proptest::collection::vec(any::<u8>(), 1..30), // Value
            any::<TreeOp>(),
        ),
        len_range, // Length of the list
    );

    let test = |input: Vec<(Vec<u8>, Vec<u8>, TreeOp)>| {
        tree_insert_read_delete_scan(input, storage);
        Ok(())
    };

    let mut runner = TestRunner::new(config);
    runner.run(&strategy, test).unwrap();
}

#[test]
fn test_tree_insert_read_0() {
    leaf_insert_read_inner(1..50, &":memory:");
}

#[test]
fn test_tree_insert_read_1() {
    leaf_insert_read_inner(50..500, &":memory:");
}

#[test]
fn test_tree_insert_read_2() {
    leaf_insert_read_inner(500..5000, &":memory:");
}

#[test]
fn test_tree_insert_read_3() {
    leaf_insert_read_inner(1000..10000, &":memory:");
}

#[test]
fn test_tree_insert_read_4() {
    leaf_insert_read_inner(1000..10000, &":cache:");
}

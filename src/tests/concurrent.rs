use std::hint::black_box;

use crate::sync::{thread, Arc};
use crate::BfTree;

#[cfg(feature = "shuttle")]
use shuttle::rand::{thread_rng, Rng};

#[cfg(not(feature = "shuttle"))]
use rand::{thread_rng, Rng};

fn make_key(key: u32, len: usize) -> Vec<u8> {
    let bytes = key.to_ne_bytes();
    bytes.into_iter().cycle().take(len).collect::<Vec<_>>()
}

fn sanity_check_value(value: &[u8], expected_len: usize) {
    assert!(value.len() == expected_len);
    let ptr = value.as_ptr() as *const u32;
    let first_v = unsafe { *ptr };
    let u32_cnt = expected_len / std::mem::size_of::<u32>();
    for i in 0..u32_cnt {
        let v = unsafe { *ptr.add(i) };
        assert_eq!(v, first_v);
    }
}
#[test]
fn concurrent_ops() {
    let bf_tree = Arc::new(BfTree::new(":memory:", 4096 * 2));

    const OP_RANGE: usize = 4;

    let config = ConcurrentConfig::new_large();

    let mut join_handles = Vec::with_capacity(config.thread_cnt);

    for _ in 0..config.thread_cnt {
        let tree_clone = bf_tree.clone();
        let handle = thread::spawn(move || {
            let mut buffer = Vec::with_capacity(4096);
            unsafe { buffer.set_len(4096) };

            let mut rng = thread_rng();
            let current_tid = thread::current().id();
            black_box(current_tid);

            for op_n in 0..config.op_cnt_per_thread {
                black_box(op_n);
                match rng.gen_range(0..OP_RANGE) {
                    0..=1 => {
                        // insert
                        let key = rng.gen_range(0..config.key_range);
                        let kv = make_key(key, config.key_len);
                        let _unused = tree_clone.insert(&kv, &kv);
                    }
                    2 => {
                        // read
                        let key = rng.gen_range(0..config.key_range);
                        let kv = make_key(key, config.key_len);
                        let cnt = tree_clone.read(&kv, &mut buffer);

                        if cnt > 0 {
                            sanity_check_value(&buffer[..cnt as usize], kv.len());
                        }
                    }
                    3 => {
                        // delete
                        let key = rng.gen_range(0..config.key_range);
                        let kv = make_key(key, config.key_len);
                        tree_clone.delete(&kv);
                    }
                    _ => unreachable!(),
                }
            }
        });
        join_handles.push(handle);
    }

    for h in join_handles {
        h.join().unwrap();
    }
}

struct ConcurrentConfig {
    thread_cnt: usize,
    op_cnt_per_thread: usize,
    key_range: u32,
    key_len: usize,
}

impl ConcurrentConfig {
    #[allow(dead_code)]
    fn new_large() -> Self {
        Self {
            thread_cnt: 3,
            op_cnt_per_thread: 400,
            key_range: 1_000,
            key_len: 400,
        }
    }

    #[allow(dead_code)]
    fn new_small() -> Self {
        Self {
            thread_cnt: 2,
            op_cnt_per_thread: 200,
            key_range: 200,
            key_len: 400,
        }
    }
}

#[cfg(feature = "shuttle")]
#[test]
fn shuttle_bf_tree_concurrent_operations() {
    use std::{path::PathBuf, str::FromStr};

    tracing_subscriber::fmt()
        .with_ansi(true)
        .with_thread_names(false)
        .with_target(false)
        .init();

    let mut config = shuttle::Config::default();
    config.max_steps = shuttle::MaxSteps::None;
    config.failure_persistence =
        shuttle::FailurePersistence::File(Some(PathBuf::from_str("target").unwrap()));
    let mut runner = shuttle::PortfolioRunner::new(true, config);

    let available_cores = std::thread::available_parallelism().unwrap().get().min(4);

    for _i in 0..available_cores {
        runner.add(shuttle::scheduler::PctScheduler::new(10, 4_000));
    }

    runner.run(concurrent_ops);
}

#[cfg(feature = "shuttle")]
#[test]
fn shuttle_ht_replay() {
    // install global collector configured based on RUST_LOG env var.
    tracing_subscriber::fmt()
        .with_ansi(true)
        .with_thread_names(false)
        .with_target(false)
        .init();

    shuttle::replay_from_file(concurrent_ops, "target/schedule000.txt");
}

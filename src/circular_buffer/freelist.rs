use crate::sync::{<PERSON><PERSON><PERSON>, MutexGuard};
use std::{
    ops::{<PERSON><PERSON>, DerefMut},
    ptr::NonNull,
    sync::TryLockError,
};

/// Block size to use
///
const SIZE_CLASSES: &[usize] = &[4096, 2048, 1024, 512, 256, 64];

fn size_class_smaller_than(size: usize) -> usize {
    SIZE_CLASSES
        .iter()
        .position(|&s| s <= size)
        .expect("size too small")
}

fn size_class_larger_than(size: usize) -> usize {
    let pos = SIZE_CLASSES
        .iter()
        .rev()
        .position(|&s| s >= size)
        .expect("size too large");
    SIZE_CLASSES.len() - 1 - pos
}

#[derive(Debug)]
pub(crate) struct ListNode {
    pub next: *mut ListNode,
}

impl ListNode {
    /// casting a *u8 to *ListNode can be UB because of alignment
    pub(crate) fn from_u8_ptr_unchecked(addr: *mut u8) -> *mut ListNode {
        debug_assert!(addr as usize % std::mem::align_of::<ListNode>() == 0);
        addr as *mut ListNode
    }
}

#[derive(Debug)]
pub enum FreeListError {
    WouldBlock,
    SizeTooSmall,
    // Add more error types as needed
}

#[derive(Debug)]
pub(super) struct FreeList {
    list_heads: [Mutex<*mut ListNode>; SIZE_CLASSES.len()],
}

unsafe impl Send for FreeList {}
unsafe impl Sync for FreeList {}

impl FreeList {
    pub(super) fn new() -> Self {
        let list_heads: [Mutex<*mut ListNode>; SIZE_CLASSES.len()] =
            std::array::from_fn(|_| Mutex::new(std::ptr::null_mut()));

        Self { list_heads }
    }

    /// Returns the ptr and the size class.
    /// return size >= requested size.
    #[cfg_attr(feature = "tracing", tracing::instrument)]
    pub(super) fn remove(&self, size: usize) -> Option<NonNull<u8>> {
        let size_class_idx = size_class_larger_than(size);
        let mut node = self.list_heads[size_class_idx].lock().unwrap();

        if node.is_null() {
            return None;
        }
        let old = *node.deref();
        let new = unsafe { (*(*node.deref())).next };
        *node.deref_mut() = new;
        Some(NonNull::new(old as *mut u8).unwrap())
    }

    /// Error if can't acquire lock
    ///
    /// Returns the lock guard so that caller can set appropriate metadata.
    #[cfg_attr(feature = "tracing", tracing::instrument)]
    pub(super) fn try_add(
        &self,
        ptr: *mut u8,
        size: usize,
    ) -> Result<MutexGuard<*mut ListNode>, FreeListError> {
        if size < *SIZE_CLASSES.last().unwrap() {
            return Err(FreeListError::SizeTooSmall);
        }

        let size_class_idx = size_class_smaller_than(size);
        let mut head = match self.list_heads[size_class_idx].try_lock() {
            Ok(v) => v,
            Err(TryLockError::WouldBlock) => return Err(FreeListError::WouldBlock),
            Err(TryLockError::Poisoned(_)) => panic!("poisoned lock"),
        };
        debug_assert!(std::mem::size_of::<ListNode>() <= SIZE_CLASSES[size_class_idx]);
        debug_assert!(std::mem::align_of::<ListNode>() <= SIZE_CLASSES[size_class_idx]);

        let node = ListNode::from_u8_ptr_unchecked(ptr);
        unsafe { (*node).next = *head };
        *head = node;
        Ok(head)
    }

    /// Returns false if not found
    ///
    /// Assumption:
    ///   No two threads may call this function at the same time!
    ///
    #[cfg_attr(feature = "tracing", tracing::instrument)]
    pub(super) fn find_and_remove(&self, ptr: *mut u8, size: usize) -> bool {
        let size_class_idx = size_class_smaller_than(size);
        let mut node_guard = self.list_heads[size_class_idx].lock().unwrap();
        let mut node = *node_guard.deref_mut();
        let mut prev: *mut ListNode = std::ptr::null_mut();
        loop {
            if node.is_null() {
                return false;
            }
            if node as *mut u8 == ptr {
                if prev.is_null() {
                    *node_guard.deref_mut() = unsafe { (*node).next };
                } else {
                    unsafe { (*prev).next = (*node).next };
                }
                return true;
            }
            prev = node;
            node = unsafe { (*node).next };
        }
    }
}

#[cfg(test)]
mod test {
    use super::*;

    #[test]
    fn test_new_initialization() {
        let free_list = FreeList::new();
        // Verify each list head is initialized to null.
        for head in free_list.list_heads.iter() {
            assert!(head.lock().unwrap().is_null());
        }
    }

    #[test]
    fn test_remove_empty() {
        let free_list = FreeList::new();
        assert!(free_list.remove(64).is_none());
    }

    #[test]
    fn test_add_and_remove() {
        let free_list = FreeList::new();
        let block = Box::into_raw(Box::new([0u8; 64])); // Allocate a block of 64 bytes.
        let lock_guard = free_list.try_add(block as *mut u8, 64).unwrap();
        drop(lock_guard);
        let removed = free_list.remove(64).unwrap();
        assert_eq!(removed.as_ptr(), block as *mut u8);
        // Cleanup
        unsafe {
            _ = Box::from_raw(block);
        }
    }

    #[test]
    fn test_find_and_remove() {
        let free_list = FreeList::new();
        let block = Box::into_raw(Box::new([0u8; 64]));
        let lock_guard = free_list.try_add(block as *mut u8, 64).unwrap();
        drop(lock_guard);
        assert!(free_list.find_and_remove(block as *mut u8, 64));
        // Verify removal
        assert!(!free_list.find_and_remove(block as *mut u8, 64));
        // Cleanup
        unsafe {
            _ = Box::from_raw(block);
        }
    }

    use crate::sync::thread;
    use crate::sync::{Arc, Barrier};

    #[test]
    fn test_multithreaded_access() {
        let free_list = Arc::new(FreeList::new());
        let n_threads = 10;
        let barrier = Arc::new(Barrier::new(n_threads));
        let mut handles = vec![];

        for _ in 0..n_threads {
            let fl = Arc::clone(&free_list);
            let b = Arc::clone(&barrier);
            handles.push(thread::spawn(move || {
                b.wait(); // Ensure all threads start simultaneously.
                let block = Box::into_raw(Box::new([0u8; 64]));
                if let Ok(lock_guard) = fl.try_add(block as *mut u8, 64) {
                    drop(lock_guard);
                    let removed = fl.find_and_remove(block as *mut u8, 64);
                    assert!(removed);
                }
                // Cleanup
                unsafe {
                    _ = Box::from_raw(block);
                }
            }));
        }

        for handle in handles {
            handle.join().unwrap();
        }
    }
}

mod inner_node;
pub(crate) mod leaf_node;
mod node_meta;
mod page_id;

pub(crate) use inner_node::{InnerNode, InnerNodeBuilder};
pub(crate) use leaf_node::LeafNode;
pub(crate) use page_id::PageID;

pub(crate) const DEFAULT_LEAF_NODE_SIZE: usize = 4096;
pub(crate) const INNER_NODE_SIZE: usize = 4096;
pub(crate) const DISK_PAGE_SIZE: usize = DEFAULT_LEAF_NODE_SIZE;

pub(crate) const MINI_PAGE_SIZE_64: usize = 64;
pub(crate) const MINI_PAGE_SIZE_256: usize = 256;
pub(crate) const MINI_PAGE_SIZE_512: usize = 512;
pub(crate) const MINI_PAGE_SIZE_1024: usize = 1024;
pub(crate) const MINI_PAGE_SIZE_2048: usize = 2048;
pub(crate) const MINI_PAGE_SIZE_4096: usize = 4096;
pub(crate) const FENCE_KEY_CNT: usize = 2;

pub(crate) const KV_META_SIZE: usize = 8;

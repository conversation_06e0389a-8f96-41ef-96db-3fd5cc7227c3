repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.0.1
    hooks:
      - id: trailing-whitespace
        exclude_types: [diff]
      - id: check-toml

  # Add local hooks for Rust
  - repo: local
    hooks:
      - id: cargo-fmt
        name: cargo fmt
        description: This hook runs cargo fmt to check Rust code formatting.
        entry: cargo fmt -- --check
        language: system
        types: [rust]
        pass_filenames: false

      - id: cargo-clippy
        name: cargo clippy
        description: This hook runs cargo clippy to lint Rust code.
        entry: cargo clippy -- -D warnings
        language: system
        types: [rust]
        pass_filenames: false

      - id: cargo-test
        name: cargo test
        description: This hook runs cargo test.
        entry: cargo test
        language: system
        types: [rust]
        pass_filenames: false

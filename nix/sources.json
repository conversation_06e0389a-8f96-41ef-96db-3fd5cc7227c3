{"libnvme": {"branch": "master", "description": "nvme client library", "homepage": "https://github.com/linux-nvme/libnvme", "owner": "linux-nvme", "repo": "libnvme", "rev": "v1.0-rc3", "sha256": "0wkr0axmm4wqjncb6x7fjyg0wxjdi9f2qd3df6ph671qh5ralrjy", "type": "tarball", "url": "https://github.com/linux-nvme/libnvme/archive/v1.0-rc3.tar.gz", "url_template": "https://github.com/<owner>/<repo>/archive/<rev>.tar.gz"}, "nvme-cli": {"branch": "master", "description": "NVM-Express user space tooling for Linux.", "homepage": "https://github.com/linux-nvme/nvme-cli", "owner": "linux-nvme", "repo": "nvme-cli", "rev": "v1.16", "sha256": "130x5cf6kkcnyg5qd35igii249ysfjnbxp1pxfwkickmqg3d007z", "type": "tarball", "url": "https://github.com/linux-nvme/nvme-cli/archive/refs/tags/v1.16.tar.gz", "url_template": "https://github.com/<owner>/<repo>/archive/<rev>.tar.gz"}, "niv": {"branch": "master", "description": "Easy dependency management for Nix projects", "homepage": "https://github.com/nmattia/niv", "owner": "nmattia", "repo": "niv", "rev": "1819632b5823e0527da28ad82fecd6be5136c1e9", "sha256": "08jz17756qchq0zrqmapcm33nr4ms9f630mycc06i6zkfwl5yh5i", "type": "tarball", "url": "https://github.com/nmattia/niv/archive/1819632b5823e0527da28ad82fecd6be5136c1e9.tar.gz", "url_template": "https://github.com/<owner>/<repo>/archive/<rev>.tar.gz"}, "nixpkgs": {"branch": "master", "description": "A read-only mirror of NixOS/nixpkgs tracking the released channels. Send issues and PRs to", "homepage": "https://github.com/NixOS/nixpkgs", "owner": "NixOS", "repo": "nixpkgs", "rev": "22.11", "sha256": "11w3wn2yjhaa5pv20gbfbirvjq6i3m7pqrq2msf0g7cv44vijwgw", "type": "tarball", "url": "https://github.com/NixOS/nixpkgs/archive/22.11.tar.gz", "url_template": "https://github.com/<owner>/<repo>/archive/<rev>.tar.gz"}, "rust-overlay": {"branch": "master", "description": "Pure and reproducible nix overlay for binary distributed rust toolchains", "homepage": "", "owner": "oxalica", "repo": "rust-overlay", "rev": "673e2d3d2a3951adc6f5e3351c9fce6ad130baed", "sha256": "sha256:1vgjkaikv8lwm3kmb4jbc96kxhdy38nmf1v4s7nmx6j4bd8pmlyd", "type": "tarball", "url": "https://github.com/oxalica/rust-overlay/archive/673e2d3d2a3951adc6f5e3351c9fce6ad130baed.tar.gz", "url_template": "https://github.com/<owner>/<repo>/archive/<rev>.tar.gz"}}